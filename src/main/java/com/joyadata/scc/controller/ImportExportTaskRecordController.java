package com.joyadata.scc.controller;

import com.alibaba.fastjson.JSONObject;
import com.joyadata.cms.model.User;
import com.joyadata.controller.BaseController;
import com.joyadata.cores.auth.annotation.Auth;
import com.joyadata.exception.AppErrorException;
import com.joyadata.feign.service.JoyaFeignService;
import com.joyadata.feign.util.FeignFactory;
import com.joyadata.model.web.Response;
import com.joyadata.model.web.ResponseFactory;
import com.joyadata.scc.model.ImportExportTaskRecord;
import com.joyadata.scc.model.imports.integration.IntegrationDTO;
import com.joyadata.scc.service.ImportService;
import com.joyadata.scc.service.ProcessDefinitionService;
import com.joyadata.scc.util.FileUtils;
import com.joyadata.scc.util.LogReaderUtil;
import com.joyadata.scc.util.NetUtils;
import com.joyadata.tms.model.Product;
import com.joyadata.util.ThreadLocalUserUtil;
import groovy.lang.Tuple2;
import groovy.lang.Tuple3;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2025/8/8 13:59
 */
@Slf4j
@RestController
@CrossOrigin
public class ImportExportTaskRecordController extends BaseController<ImportExportTaskRecord> {
    @Autowired
    private ImportService importService;
    @Autowired
    private StringRedisTemplate redisTemplate;
    private String IMPORT_KEY = "dedp:scc:import:";

    @Value("${export_base_path:}")
    private String exportBasePath;
    Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds = null;

    private JoyaFeignService<Product> productJoyaFeignService = FeignFactory.make(Product.class);
    @Autowired
    private ProcessDefinitionService processDefinitionService;

    /**
     * 获取导出基础路径，如果未配置则使用系统相关的默认路径
     */
    private String getExportBasePath() {
        if (StringUtils.isNotBlank(exportBasePath)) {
            return exportBasePath + "/export_import_files/";
        }
        // 根据操作系统选择合适的默认路径
        String osName = System.getProperty("os.name").toLowerCase();
        if (osName.contains("windows")) {
            return "D:/tmp/export";
        } else {
            return "/tmp/export";
        }
    }

    // 主异步提交线程池，用于处理批次提交任务
    private final ThreadPoolExecutor mainAsyncExecutor = new ThreadPoolExecutor(5, 5, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10000),
            new ThreadFactory() {
                private int counter = 0;

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "BatchSubmit-" + (++counter));
                    thread.setDaemon(true);
                    return thread;
                }
            }, new ThreadPoolExecutor.CallerRunsPolicy()
    );

    // 工作流处理线程池，容量为10，用于处理具体的工作流
    private final ThreadPoolExecutor workflowExecutor = new ThreadPoolExecutor(5, 5, 60L, TimeUnit.SECONDS, new LinkedBlockingQueue<>(10000),
            new ThreadFactory() {
                private int counter = 0;

                @Override
                public Thread newThread(Runnable r) {
                    Thread thread = new Thread(r, "WorkflowProcess-" + (++counter));
                    thread.setDaemon(true);
                    return thread;
                }
            }, new ThreadPoolExecutor.CallerRunsPolicy()
    );

    @Override
    public Response<?> add(HttpServletRequest request, String id, ImportExportTaskRecord importExportTaskRecord) throws Exception {
        String projectId = request.getHeader("projectId");
        if (StringUtils.isBlank(projectId)) {
            throw new AppErrorException("项目id不能为空");
        }
        String productId = request.getHeader("productId");
        if (StringUtils.isBlank(productId)) {
            throw new AppErrorException("产品id不能为空");
        }
        User user = ThreadLocalUserUtil.getUser(User.class);
        importExportTaskRecord.setBatchNo(getBatchNo(user.getTenantCode()));
        importExportTaskRecord.setType(1);
        importExportTaskRecord.setState(2);
        importExportTaskRecord.setIp(NetUtils.getHost());
        importExportTaskRecord.setProjectId(projectId);
        importExportTaskRecord.setProductId(productId);
        if (null == importExportTaskRecord.getProcessDefinitionCodes()) {
            return ResponseFactory.makeWarning("导出工作流不能未空");
        }
        Tuple3<Integer, Integer, List<String>> tuple3 = importService.getExportTaskCount(importExportTaskRecord.getProcessDefinitionCodes());
        importExportTaskRecord.setWorkflowCount(tuple3.getV1());
        importExportTaskRecord.setTaskCount(tuple3.getV2());
        importExportTaskRecord.setDatasourceCount(tuple3.getV3().stream().distinct().collect(Collectors.toList()).size());
        // 先保存记录
        Response<?> response = super.add(request, id, importExportTaskRecord);

        // 异步提交处理processDefinitionCodes（整个方法异步执行）
        mainAsyncExecutor.submit(() -> {
            String batchNo = importExportTaskRecord.getBatchNo();
            try {
                // 设置MDC，用于日志文件分离
                setupMDC(batchNo, user.getTenantCode());
                log.info("开始批次异步处理，批次号是{}", batchNo);

                submitAsyncProcessing(importExportTaskRecord, projectId, productId, user.getTenantCode());
            } catch (Exception e) {
                log.error("异步提交处理失败：", e);
                updateTaskRecordState(importExportTaskRecord.getId(), batchNo, 0, "异步提交处理失败：" + e.getMessage(), null);
            } finally {
                // 清理MDC
                clearMDC();
            }
        });

        return response;
    }

    /**
     * 导入工作流定义
     *
     * @param file 导入的tar包文件
     * @param name 任务名称
     * @return 导入结果
     */
    @Auth
    @PostMapping("/importExport/import")
    public Response<Map<String, Object>> importWorkflows(
            @RequestParam("file") MultipartFile file,
            @RequestParam("name") String name,
            HttpServletRequest request) {

        try {
            User user = ThreadLocalUserUtil.getUser(User.class);
            //这两个值都应该是集成的，目前这里是调度的，下面转换为集成的。projectId都一样，不需要处理
            // productId 需要转换
            String projectId = request.getHeader("projectId");
            String sccProductId = request.getHeader("productId");
            String productId = productJoyaFeignService.getQuery().eq("code", "integration").oneValue("id", String.class);

            // 参数验证
            if (file == null || file.isEmpty()) {
                throw new AppErrorException("导入文件不能为空");
            }

            if (!file.getOriginalFilename().endsWith(".tar")) {
                throw new AppErrorException("只支持tar格式的文件");
            }

            if (StringUtils.isBlank(name)) {
                throw new AppErrorException("任务名称不能为空");
            }

            // 生成批次号
            String batchNo = getBatchNo(user.getTenantCode());

            // 创建导入任务记录
            ImportExportTaskRecord importTaskRecord = new ImportExportTaskRecord();
            importTaskRecord.setBatchNo(batchNo);
            importTaskRecord.setName(name);
            importTaskRecord.setType(0); // 1-导入，0-导出
            importTaskRecord.setState(2); // 0-失败，1成功，2运行中
            importTaskRecord.setCreateTime(new Date());
            importTaskRecord.setProductId(sccProductId);
            importTaskRecord.setProjectId(projectId);
            importTaskRecord.setIp(NetUtils.getHost());

            // 保存任务记录到数据库
            // TODO: 调用service保存记录
            getService().add(importTaskRecord);

            Map<String, Object> response = new HashMap<>();
            response.put("batchNo", batchNo);
            response.put("message", "导入任务已提交，正在后台处理");
            // 异步处理导入任务
            mainAsyncExecutor.submit(() -> {
                try {
                    // 设置MDC，用于日志文件分离
                    setupMDC(batchNo, user.getTenantCode());
                    log.info("开始批次导入处理，批次号：{}", batchNo);

                    processImportTask(importTaskRecord, file, getExportBasePath(), projectId, productId, sccProductId, user);

                } catch (Exception e) {
                    log.error("导入任务处理失败：", e);
                    updateTaskRecordState(importTaskRecord.getId(), batchNo, 0, "导入任务处理失败：" + e.getMessage(), null);
                } finally {
                    // 清理MDC
                    clearMDC();
                }
            });

            return ResponseFactory.makeSuccess(response);

        } catch (Exception e) {
            log.error("提交导入任务失败：", e);
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("message", "提交导入任务失败：" + e.getMessage());
            return ResponseFactory.makeError(errorResponse);
        }
    }

    /**
     * 异步提交处理方法
     * 将importExportTaskRecord中的processDefinitionCodes根据逗号拆开，然后提交给异步线程来执行
     */
    private void submitAsyncProcessing(ImportExportTaskRecord importExportTaskRecord, String projectId, String productId, String tenantCode) {
        String batchNo = importExportTaskRecord.getBatchNo();
        try {
            String processDefinitionCodes = importExportTaskRecord.getProcessDefinitionCodes();
            if (StringUtils.isBlank(processDefinitionCodes)) {
                log.warn("工作流定义code为空，跳过异步处理");
                updateTaskRecordState(importExportTaskRecord.getId(), batchNo, 0, "工作流定义code为空", null);
                return;
            }

            // 根据逗号拆分processDefinitionCodes
            List<String> codeList = Arrays.stream(processDefinitionCodes.split(","))
                    .map(String::trim)
                    .filter(StringUtils::isNotBlank)
                    .collect(Collectors.toList());

            if (codeList.isEmpty()) {
                log.warn("工作流定义code格式不正确，跳过异步处理");
                updateTaskRecordState(importExportTaskRecord.getId(), batchNo, 0, "工作流定义code格式不正确", null);
                return;
            }

            log.info("开始异步处理导入导出任务，工作流数量：{}, 工作流code：{}",
                    codeList.size(), processDefinitionCodes);

            // 创建批次专用的工作目录
            String batchWorkDir = createBatchWorkDirectory(batchNo, tenantCode);
            log.info("创建批次工作目录：{}", batchWorkDir);

            // 用于跟踪完成的任务数量
            AtomicInteger completedCount = new AtomicInteger(0);
            AtomicInteger failedCount = new AtomicInteger(0);
            int totalCount = codeList.size();

            // 为每个processDefinitionCode提交异步任务到工作流处理线程池
            for (String code : codeList) {
                workflowExecutor.submit(() -> {
                    try {
                        // 在工作流处理线程中也设置MDC
                        setupMDC(batchNo, tenantCode);

                        processDefinitionCode(code, productId, projectId, batchWorkDir, tenantCode, batchNo);

                        // 处理成功
                        int completed = completedCount.incrementAndGet();
                        log.info("工作流 {} 处理成功，进度：{}/{}", code, completed, totalCount);

                        if (completed == totalCount) {
                            // 所有任务完成，创建tar包（只打包成功的JSON文件）
                            int failed = failedCount.get();
                            createTarPackage(batchNo, batchWorkDir, importExportTaskRecord, completed - failed, failed);
                        }

                    } catch (Exception e) {
                        // 处理失败，记录失败数量和日志
                        int failed = failedCount.incrementAndGet();
                        int completed = completedCount.incrementAndGet();

                        log.error("工作流 {} 处理失败，进度：{}/{}，失败数：{}", code, completed, totalCount, failed);
                        log.error("失败原因：", e);

                        if (completed == totalCount) {
                            // 所有任务完成，创建tar包（只打包成功的JSON文件）
                            int successCount = completed - failed;
                            log.info("批次处理完成，成功：{}，失败：{}，开始打包成功的文件", successCount, failed);
                            createTarPackage(batchNo, batchWorkDir, importExportTaskRecord, successCount, failed);
                        }

                    } finally {
                        // 清理MDC
                        clearMDC();
                    }
                });
            }

        } catch (Exception e) {
            log.error("提交异步任务失败：", e);
            updateTaskRecordState(importExportTaskRecord.getId(), batchNo, 0, "提交异步任务失败：" + e.getMessage(), null);
        }
    }

    /**
     * 处理单个工作流定义code的业务逻辑
     */
    private void processDefinitionCode(String processDefinitionCode, String productId, String projectId, String batchWorkDir, String tenantCode, String batchNo) {
        log.info("开始处理工作流定义code：{}", processDefinitionCode);

        try {
            // 1. 创建该工作流的JSON文件路径
            String jsonFileName = String.format("%s_%s.json", processDefinitionCode, System.currentTimeMillis());
            String jsonFilePath = Paths.get(batchWorkDir, jsonFileName).toString();

            log.info("工作流 {} 的JSON文件路径：{}", processDefinitionCode, jsonFilePath);

            log.info("开始调用importService处理 {} 项目的工作流：{}", projectId, processDefinitionCode);
            IntegrationDTO processDefinitionJson = importService.exportProcessJson(productId, projectId, processDefinitionCode, tenantCode, batchNo, true);
            // 创建示例JSON文件（实际应该是导出的工作流定义）
            createSampleJsonFile(jsonFilePath, JSONObject.toJSONString(processDefinitionJson));

            log.info("工作流定义code {} 处理完成，JSON文件已生成：{}", processDefinitionCode, jsonFilePath);

        } catch (Exception e) {
            log.error("处理工作流定义code {} 时发生异常：{}", processDefinitionCode, e);
            // 抛出异常，让上层处理失败逻辑
            throw new RuntimeException("工作流 " + processDefinitionCode + " 处理失败", e);
        }
    }

    /**
     * 创建批次专用的工作目录
     * 目录结构：{exportBasePath}/{tenantCode}/download/{batchNo}/
     */
    private String createBatchWorkDirectory(String batchNo, String tenantCode) {
        try {
            String batchDir = Paths.get(getExportBasePath(), tenantCode, "download", batchNo).toString();
            Path batchPath = Paths.get(batchDir);

            if (!Files.exists(batchPath)) {
                Files.createDirectories(batchPath);
                log.info("创建批次目录：{}", batchDir);
            }

            return batchDir;
        } catch (IOException e) {
            log.error("创建批次目录失败：", e);
            throw new RuntimeException("创建批次目录失败", e);
        }
    }

    /**
     * 创建tar包
     */
    private void createTarPackage(String batchNo, String batchWorkDir, ImportExportTaskRecord record, int successCount, int failedCount) {
        try {
            log.info("开始创建tar包，工作目录：{}", batchWorkDir);
            // 获取工作目录中的所有JSON文件
            File workDir = new File(batchWorkDir);
            File[] jsonFiles = workDir.listFiles((dir, name) -> name.endsWith(".json"));
            if (jsonFiles == null || jsonFiles.length == 0) {
                log.warn("工作目录中没有找到JSON文件");
                updateTaskRecordState(record.getId(), batchNo, 0, "没有找到可打包的JSON文件", null);
                return;
            }

            log.info("找到 {} 个JSON文件准备打包", jsonFiles.length);

            // tar包放在工作目录的上级目录，避免包含自身
            String tarFileName = String.format("%s_export.tar", batchNo);
            String tarFilePath = Paths.get(workDir.getParent(), tarFileName).toString();

            // 构建tar命令，只打包JSON文件
            String[] tarCommand = new String[jsonFiles.length + 5];
            tarCommand[0] = "tar";
            tarCommand[1] = "-cf";
            tarCommand[2] = tarFilePath;
            tarCommand[3] = "-C";
            tarCommand[4] = batchWorkDir;

            // 添加所有JSON文件名（相对路径）
            for (int i = 0; i < jsonFiles.length; i++) {
                tarCommand[i + 5] = jsonFiles[i].getName();
            }

            log.info("执行tar命令，打包文件：{}", Arrays.stream(jsonFiles).map(File::getName).collect(Collectors.joining(", ")));

            ProcessBuilder pb = new ProcessBuilder(tarCommand);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("tar包创建成功：{}", tarFilePath);

                // 清理JSON文件
                cleanupJsonFiles(jsonFiles);

                // 删除空的工作目录
                if (workDir.delete()) {
                    log.info("工作目录已清理：{}", batchWorkDir);
                } else {
                    log.warn("工作目录清理失败：{}", batchWorkDir);
                }

                // 根据成功和失败数量决定最终状态
                String statusMessage;
                int finalState;
                if (failedCount > 0) {
                    finalState = 2; // 部分成功状态
                    statusMessage = String.format("批次处理完成，成功：%d，失败：%d，tar包路径：%s", successCount, failedCount, tarFilePath);
                    log.warn("批次处理完成，有部分失败 - 成功：{}，失败：{}，tar包已创建", successCount, failedCount);
                } else {
                    finalState = 1; // 完全成功状态
                    statusMessage = String.format("批次处理完成，全部成功：%d，tar包路径：%s", successCount, tarFilePath);
                    log.info("批次处理完成，全部成功：{}，tar包已创建", successCount);
                }

                updateTaskRecordState(record.getId(), batchNo, finalState, statusMessage, tarFilePath);
            } else {
                log.error("tar包创建失败，退出码：{}", exitCode);
                updateTaskRecordState(record.getId(), batchNo, 0, "tar包创建失败", null);
            }

        } catch (Exception e) {
            log.error("创建tar包时发生异常：", e);
            updateTaskRecordState(record.getId(), batchNo, 0, "创建tar包异常：" + e.getMessage(), null);
        }
    }

    /**
     * 创建示例JSON文件（实际应该替换为真实的工作流导出逻辑）
     */
    private void createSampleJsonFile(String jsonFilePath, String processDefinitionJson) {
        try {
            Files.write(Paths.get(jsonFilePath), processDefinitionJson.getBytes());
            log.info("JSON文件创建成功：{}", jsonFilePath);
        } catch (IOException e) {
            log.error("创建JSON文件失败：", e);
            throw new RuntimeException("创建JSON文件失败", e);
        }
    }

    /**
     * 清理JSON文件
     */
    private void cleanupJsonFiles(File[] jsonFiles) {
        try {
            int deletedCount = 0;
            for (File jsonFile : jsonFiles) {
                if (jsonFile.delete()) {
                    deletedCount++;
                    log.debug("删除JSON文件：{}", jsonFile.getName());
                } else {
                    log.warn("删除JSON文件失败：{}", jsonFile.getName());
                }
            }
            log.info("JSON文件清理完成，成功删除 {}/{} 个文件", deletedCount, jsonFiles.length);
        } catch (Exception e) {
            log.error("清理JSON文件时发生异常：", e);
        }
    }

    /**
     * 更新任务记录状态
     */
    private void updateTaskRecordState(String recordId, String batchNo, int state, String message, String tarFilePath) {
        try {
            ImportExportTaskRecord record = getService().getQuery().eq("id", recordId).one();
            if (record != null) {
                record.setState(state);
                record.setFilePath(tarFilePath);
                if (StringUtils.isNotBlank(message)) {
                    // 可以考虑添加一个备注字段来记录错误信息
                    log.info("[RecordId:{},batchNo:{}] 更新任务状态：{}, 消息：{}", recordId, batchNo, state, message);
                }
                this.getService().update(record);
            }
        } catch (Exception e) {
            log.error("[RecordId:{},batchNo:{}] 更新任务状态失败：", recordId, batchNo, e);
        }
    }

    /**
     * 设置MDC上下文，用于日志文件分离
     * 文件路径格式：tenantCode/batchNo.log
     */
    private void setupMDC(String batchNo, String tenantCode) {
        MDC.put("batchNo", batchNo);
        MDC.put("tenantCode", tenantCode);
        // 设置完整的文件路径（不包含扩展名，logback会自动添加.log）
        String logFilePath = String.format("%s/%s", tenantCode, batchNo);
        MDC.put("logFilePath", logFilePath);
    }

    /**
     * 清理MDC上下文
     */
    private void clearMDC() {
        MDC.clear();
    }

    private String getBatchNo(String tenantCode) {
        String dateStr = new SimpleDateFormat("yyyyMMdd").format(new Date());
        String key = IMPORT_KEY + tenantCode + ":" + dateStr;
        Long serialNumber = redisTemplate.opsForValue().increment(key);
        if (serialNumber != null && serialNumber == 1L) {
            redisTemplate.expire(key, 25, TimeUnit.HOURS);
        }
        return dateStr + String.format("%06d", serialNumber);
    }

    /**
     * 处理导入任务 - 重构版本，支持单线程预处理 + 并发处理 + 统一同步
     */
    private void processImportTask(ImportExportTaskRecord importTaskRecord, MultipartFile file,
                                   String exportBasePath, String projectId, String productId, String sccProductId, User user) {
        String batchNo = importTaskRecord.getBatchNo();

        try {
            log.info("开始处理导入任务，批次号：{}", batchNo);
            long startTime = System.currentTimeMillis();
            // ========== 阶段1：单线程预处理阶段 ==========
            ImportPreprocessResult preprocessResult = executePreprocessPhase(importTaskRecord, file, exportBasePath, projectId, user, batchNo, sccProductId);
            log.info("第一阶段处理完成，耗时 {}", (System.currentTimeMillis() - startTime));
            long oneStep = System.currentTimeMillis();
            // ========== 阶段2：并发处理阶段 ==========
            ImportConcurrentResult concurrentResult = executeConcurrentPhase(preprocessResult, projectId, productId, user.getTenantCode(), batchNo);
            log.info("第二阶段处理完成，耗时 {}", (System.currentTimeMillis() - oneStep));
            long twoStep = System.currentTimeMillis();
            // ========== 阶段3：统一同步阶段 ==========
            executeSyncPhase(concurrentResult, importTaskRecord, projectId, productId, batchNo);
            log.info("第三阶段处理完成，耗时 {}", (System.currentTimeMillis() - twoStep));
            long threeStep = System.currentTimeMillis();
            // ========== 阶段4：清理和状态更新 ==========
            executeCleanupPhase(preprocessResult.getTempWorkDir(), concurrentResult, importTaskRecord, batchNo);
            log.info("第四阶段处理完成，耗时 {}", (System.currentTimeMillis() - threeStep));
        } catch (Exception e) {
            log.error("处理导入任务异常，批次号：{}", batchNo, e);
            updateTaskRecordState(importTaskRecord.getId(), batchNo, 0, "导入任务处理异常：" + e.getMessage(), null);
        }
    }

    /**
     * 阶段1：单线程预处理阶段
     * 包括：文件解压、JSON文件发现、全局目录初始化、删除任务查询等
     */
    private ImportPreprocessResult executePreprocessPhase(ImportExportTaskRecord importTaskRecord, MultipartFile file,
                                                          String exportBasePath, String projectId, User user, String batchNo, String sccProductId) throws Exception {
        log.info("[BatchNo:{}] ========== 开始预处理阶段 ==========", batchNo);

        // 1. 创建临时工作目录
        String tempWorkDir = createImportWorkDirectory(batchNo, exportBasePath, user.getTenantCode());
        log.info("[BatchNo:{}] 创建导入工作目录：{}", batchNo, tempWorkDir);

        // 2. 保存上传的tar包
        String tarFilePath = saveUploadedFile(file, tempWorkDir, batchNo);
        log.info("[BatchNo:{}] 保存上传文件：{}", batchNo, tarFilePath);

        // 3. 解压tar包
        extractTarFile(tarFilePath, tempWorkDir, batchNo);
        log.info("[BatchNo:{}] 解压tar包完成", batchNo);

        // 4. 发现JSON文件
        List<String> jsonFiles = findJsonFiles(tempWorkDir);
        log.info("[BatchNo:{}] 找到 {} 个JSON文件准备导入", batchNo, jsonFiles.size());

        if (jsonFiles.isEmpty()) {
            throw new RuntimeException("未找到可导入的JSON文件");
        }

        // 5. 统计任务信息并更新记录
        Tuple3<Integer, Integer, List<String>> taskCount = importService.getTaskCountFile(jsonFiles);
        importTaskRecord.setWorkflowCount(taskCount.getV1());
        importTaskRecord.setTaskCount(taskCount.getV2());
        importTaskRecord.setDatasourceCount(taskCount.getV3().stream().distinct().collect(Collectors.toList()).size());
        importTaskRecord.setState(2);
        importTaskRecord.setType(0);
        this.getService().update(importTaskRecord);
        log.info("[BatchNo:{}] 任务统计完成 - 工作流：{}，任务：{}，数据源：{}",
                batchNo, taskCount.getV1(), taskCount.getV2(), taskCount.getV3().stream().distinct().count());

        // 6. 单线程执行全局目录初始化和删除任务查询（关键的单线程操作）
        log.info("[BatchNo:{}] 开始执行全局目录初始化和删除任务查询", batchNo);
        Tuple3<Map<String, String>, Map<String, String>, ConcurrentHashMap<String, String>> deleteTaskIds = initCatalogGlobal(jsonFiles, projectId, user, batchNo, sccProductId);
        log.info("[BatchNo:{}] 全局预处理完成，需要删除的工作流：{}，任务：{}",
                batchNo, deleteTaskIds.getV1().size(), deleteTaskIds.getV2().size());

        log.info("[BatchNo:{}] ========== 预处理阶段完成 ==========", batchNo);

        return new ImportPreprocessResult(tempWorkDir, jsonFiles, new Tuple2<>(deleteTaskIds.getV1(), deleteTaskIds.getV2()), taskCount, deleteTaskIds.getV3());
    }

    /**
     * 阶段2：并发处理阶段
     * 并发处理每个JSON文件到临时表
     */
    private ImportConcurrentResult executeConcurrentPhase(ImportPreprocessResult preprocessResult, String projectId, String productId, String tenantCode, String batchNo) throws Exception {
        log.info("[BatchNo:{}] ========== 开始并发处理阶段 ==========", batchNo);

        List<String> jsonFiles = preprocessResult.getJsonFiles();
        Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds = preprocessResult.getDeleteTaskIds();
        ConcurrentHashMap<String, String> changeTableIds = preprocessResult.getChangeTableIds();
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger failedCount = new AtomicInteger(0);
        AtomicInteger completedCount = new AtomicInteger(0);
        int totalCount = jsonFiles.size();

        // 使用CountDownLatch等待所有导入任务完成
        CountDownLatch latch = new CountDownLatch(totalCount);

        log.info("[BatchNo:{}] 开始并发处理 {} 个JSON文件", batchNo, totalCount);

        for (String jsonFile : jsonFiles) {
            workflowExecutor.submit(() -> {
                try {
                    // 在工作流处理线程中也设置MDC
                    setupMDC(batchNo, tenantCode);

                    // 传递预处理结果到每个并发任务
                    importWorkflowFromJsonWithPreprocessResult(jsonFile, batchNo, projectId, productId, changeTableIds);

                    int success = successCount.incrementAndGet();
                    int completed = completedCount.incrementAndGet();
                    log.info("[BatchNo:{}] 成功导入工作流：{}，进度：{}/{}, 成功数：{}",
                            batchNo, new File(jsonFile).getName(), completed, totalCount, success);

                } catch (Exception e) {
                    int failed = failedCount.incrementAndGet();
                    int completed = completedCount.incrementAndGet();
                    log.error("[BatchNo:{}] 导入工作流失败：{}，进度：{}/{}，失败数：{}，错误：{}",
                            batchNo, new File(jsonFile).getName(), completed, totalCount, failed, e.getMessage());
                } finally {
                    // 清理MDC
                    clearMDC();
                    // 完成一个任务
                    latch.countDown();
                }
            });
        }

        // 等待所有导入任务完成，设置超时时间
        log.info("[BatchNo:{}] 等待所有导入任务完成，总数：{}", batchNo, totalCount);
        boolean completed = latch.await(30, TimeUnit.MINUTES); // 30分钟超时

        if (!completed) {
            log.error("[BatchNo:{}] 导入任务超时，部分任务可能未完成", batchNo);
            int currentCompleted = completedCount.get();
            log.error("[BatchNo:{}] 超时时已完成：{}，剩余：{}", batchNo, currentCompleted, totalCount - currentCompleted);
        }

        int finalSuccessCount = successCount.get();
        int finalFailedCount = failedCount.get();

        log.info("[BatchNo:{}] ========== 并发处理阶段完成 ========== 成功：{}，失败：{}",
                batchNo, finalSuccessCount, finalFailedCount);

        return new ImportConcurrentResult(finalSuccessCount, finalFailedCount, totalCount);
    }

    /**
     * 阶段3：统一同步阶段
     * 将所有临时表数据一次性同步到正式表
     */
    private void executeSyncPhase(ImportConcurrentResult concurrentResult, ImportExportTaskRecord importTaskRecord,
                                  String projectId, String productId, String batchNo) throws Exception {
        log.info("[BatchNo:{}] ========== 开始统一同步阶段 ==========", batchNo);

        int finalSuccessCount = concurrentResult.getSuccessCount();
        int finalFailedCount = concurrentResult.getFailedCount();

        if (finalSuccessCount > 0) {
            log.info("[BatchNo:{}] 开始统一同步临时表数据到正式表", batchNo);
            try {
                importService.sync(batchNo, projectId, productId);
                log.info("[BatchNo:{}] 统一同步完成", batchNo);
            } catch (Exception e) {
                log.error("[BatchNo:{}] 统一同步失败：{}", batchNo, e.getMessage(), e);
                throw new RuntimeException("统一同步失败", e);
            }
        } else {
            log.warn("[BatchNo:{}] 没有成功导入的工作流，跳过同步操作", batchNo);
        }

        log.info("[BatchNo:{}] ========== 统一同步阶段完成 ==========", batchNo);
    }

    /**
     * 阶段4：清理和状态更新阶段
     */
    private void executeCleanupPhase(String tempWorkDir, ImportConcurrentResult concurrentResult,
                                     ImportExportTaskRecord importTaskRecord, String batchNo) {
        log.info("[BatchNo:{}] ========== 开始清理和状态更新阶段 ==========", batchNo);

        // 清理临时文件
        cleanupImportFiles(tempWorkDir, batchNo);

        // 更新任务状态
        int finalSuccessCount = concurrentResult.getSuccessCount();
        int finalFailedCount = concurrentResult.getFailedCount();

        String finalMessage;
        int finalState;

        if (finalFailedCount == 0) {
            finalState = 1; // 成功
            finalMessage = String.format("导入完成，成功：%d", finalSuccessCount);
            log.info("[BatchNo:{}] 批次导入完成，全部成功：{}", batchNo, finalSuccessCount);
        } else if (finalSuccessCount > 0) {
            finalState = 0; // 部分成功
            finalMessage = String.format("导入完成，成功：%d，失败：%d", finalSuccessCount, finalFailedCount);
            log.warn("[BatchNo:{}] 批次导入完成，部分成功 - 成功：{}，失败：{}", batchNo, finalSuccessCount, finalFailedCount);
        } else {
            finalState = 0; // 失败
            finalMessage = String.format("导入失败，失败：%d", finalFailedCount);
            log.error("[BatchNo:{}] 批次导入失败，全部失败：{}", batchNo, finalFailedCount);
        }

        updateTaskRecordState(importTaskRecord.getId(), batchNo, finalState, finalMessage, null);

        log.info("[BatchNo:{}] ========== 清理和状态更新阶段完成 ==========", batchNo);
    }

    /**
     * 改进的JSON文件导入方法，接收预处理结果
     */
    private void importWorkflowFromJsonWithPreprocessResult(String jsonFilePath, String batchNo, String projectId, String productId, ConcurrentHashMap<String, String> changeTableIds) {
        String fileName = new File(jsonFilePath).getName();

        try {
            log.info("[BatchNo:{}] 线程 {} 开始导入工作流：{}", batchNo, Thread.currentThread().getName(), fileName);

            // 调用ImportService的importProcessToTemp方法，将JSON导入到临时表
            // 传递预处理阶段获得的deleteTaskIds
            importService.importProcessToTemp(jsonFilePath, projectId, productId, batchNo, changeTableIds);
            log.info("[BatchNo:{}] JSON文件导入到临时表完成：{}", batchNo, fileName);

            log.info("[BatchNo:{}] 线程 {} 工作流导入成功：{}", batchNo, Thread.currentThread().getName(), fileName);

        } catch (Exception e) {
            log.error("[BatchNo:{}] 线程 {} 工作流导入失败：{}，错误：{}",
                    batchNo, Thread.currentThread().getName(), fileName, e.getMessage());
            throw new RuntimeException("工作流导入失败: " + fileName, e);
        }
    }

    private Tuple3<Map<String, String>, Map<String, String>, ConcurrentHashMap<String, String>> initCatalogGlobal(List<String> jsonFiles, String projectId, User user, String batchNo, String sccProductId) {
        // 初始化返回结果，包含两个Map
        Tuple3<Map<String, String>, Map<String, String>, ConcurrentHashMap<String, String>> resultTuple = new Tuple3<>(new HashMap<>(), new HashMap<>(), new ConcurrentHashMap<>());
        // 遍历所有JSON文件
        ConcurrentHashMap<String, String> changeTableIds = new ConcurrentHashMap<>();
        for (String jsonFile : jsonFiles) {
            try {
                // 读取JSON文件内容
                String importJson = FileUtils.readFileToString(new File(jsonFile));
                // 将JSON内容解析为IntegrationDTO对象
                IntegrationDTO integrationDTO = JSONObject.parseObject(importJson, IntegrationDTO.class);
                // 获取需要删除的流程定义信息
                Tuple2<Map<String, String>, Map<String, String>> tuple2 = importService.getProcessDefinitionNeedDelete(integrationDTO.getSccInfos(), projectId);
                //importService.getTableIdNeedChange(integrationDTO.getTaskConfigInfos(), checkDatasourceResult.getV2(), changeTableIds);
                //处理version_id替换
                importService.getTaskVersionNeedChange(changeTableIds, integrationDTO.getTaskVersionInfos());
                importService.getTaskConfigInfoNeedChange(changeTableIds, integrationDTO.getTaskConfigInfos());
                importService.getTaskConfigInfoLineNeedChange(changeTableIds, integrationDTO.getTaskConfigLineInfos());
                importService.getTaskDefinitionInfoNeedChange(changeTableIds, integrationDTO.getTaskDefinitionInfos());
                importService.getProcessNeedChange(changeTableIds, integrationDTO.getSccInfos(), tuple2, batchNo, projectId);
                importService.getTaskCatalogueNeedChanageV2(integrationDTO.getTaskCatalogueInfos(), user, projectId, changeTableIds, batchNo, new HashMap<>());
                // 将结果合并到最终返回的resultTuple中
                if (tuple2.getV1() != null) {
                    resultTuple.getV1().putAll(tuple2.getV1());
                    log.info("开始备份历史数据");
                    List<String> ids = new ArrayList<>(tuple2.getV1().values());
                    importService.backupHistoryData(ids, sccProductId, projectId, batchNo, user.getTenantCode());
                }
                if (tuple2.getV2() != null) {
                    resultTuple.getV2().putAll(tuple2.getV2());
                }
                resultTuple.getV3().putAll(changeTableIds);
            } catch (Exception e) {
                log.error("处理JSON文件失败: {}", jsonFile, e);
            }
        }

        return resultTuple;
    }


    /**
     * 创建导入工作目录
     */
    private String createImportWorkDirectory(String batchNo, String exportBasePath, String tenantCode) {
        log.info("创建导入工作目录 - exportBasePath: {}, tenantCode: {}, batchNo: {}", exportBasePath, tenantCode, batchNo);

        // 使用Paths.get()来正确构建跨平台路径，并转换为绝对路径
        Path workDirPath = Paths.get(exportBasePath, tenantCode, "import", batchNo).toAbsolutePath().normalize();
        String workDir = workDirPath.toString();

        log.info("构建的工作目录绝对路径: {}", workDir);

        File dir = new File(workDir);
        if (!dir.exists()) {
            log.info("目录不存在，开始创建: {}", workDir);
            boolean created = dir.mkdirs();
            if (!created) {
                log.error("无法创建导入工作目录: {}", workDir);
                throw new RuntimeException("无法创建导入工作目录: " + workDir);
            }
            log.info("目录创建成功: {}", workDir);
        } else {
            log.info("目录已存在: {}", workDir);
        }
        return workDir;
    }

    /**
     * 保存上传的文件
     */
    private String saveUploadedFile(MultipartFile file, String workDir, String batchNo) throws IOException {
        String fileName = String.format("%s_import.tar", batchNo);
        log.info("保存上传文件 - workDir: {}, fileName: {}", workDir, fileName);

        // 使用Paths.get()来正确构建文件路径，并转换为绝对路径
        Path filePath = Paths.get(workDir, fileName).toAbsolutePath().normalize();
        File targetFile = filePath.toFile();

        log.info("目标文件绝对路径: {}", targetFile.getAbsolutePath());

        // 确保父目录存在
        File parentDir = targetFile.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            log.info("父目录不存在，创建: {}", parentDir.getAbsolutePath());
            boolean created = parentDir.mkdirs();
            if (!created) {
                log.error("无法创建父目录: {}", parentDir.getAbsolutePath());
                throw new IOException("无法创建父目录: " + parentDir.getAbsolutePath());
            }
        }

        log.info("开始保存文件，文件大小: {} bytes", file.getSize());

        // 使用FileOutputStream来避免MultipartFile.transferTo()的路径问题
        try (FileOutputStream fos = new FileOutputStream(targetFile);
             InputStream inputStream = file.getInputStream()) {

            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
            fos.flush();
        }

        log.info("文件保存成功: {}", targetFile.getAbsolutePath());

        return filePath.toString();
    }

    /**
     * 解压tar包
     */
    private void extractTarFile(String tarFilePath, String workDir, String batchNo) {
        try {
            log.info("[BatchNo:{}] 开始解压tar包：{}", batchNo, tarFilePath);

            ProcessBuilder pb = new ProcessBuilder("tar", "-xf", tarFilePath, "-C", workDir);
            Process process = pb.start();
            int exitCode = process.waitFor();

            if (exitCode == 0) {
                log.info("[BatchNo:{}] tar包解压成功", batchNo);
            } else {
                throw new RuntimeException("tar包解压失败，退出码：" + exitCode);
            }

        } catch (Exception e) {
            log.error("[BatchNo:{}] 解压tar包失败", batchNo, e);
            throw new RuntimeException("解压tar包失败", e);
        }
    }

    /**
     * 查找JSON文件
     */
    private List<String> findJsonFiles(String workDir) {
        List<String> jsonFiles = new ArrayList<>();
        File dir = new File(workDir);
        File[] files = dir.listFiles((d, name) -> name.endsWith(".json"));

        if (files != null) {
            for (File file : files) {
                jsonFiles.add(file.getAbsolutePath());
            }
        }

        return jsonFiles;
    }

    /**
     * 根据导入模式过滤文件
     */
    private List<String> filterFilesToImport(List<String> jsonFiles, String importMode, String selectedWorkflows) {
        if ("FULL".equals(importMode)) {
            return jsonFiles;
        }

        if ("SELECTIVE".equals(importMode) && StringUtils.isNotBlank(selectedWorkflows)) {
            List<String> selectedList = Arrays.asList(selectedWorkflows.split(","));
            return jsonFiles.stream()
                    .filter(file -> {
                        String fileName = new File(file).getName();
                        return selectedList.stream().anyMatch(workflow -> fileName.contains(workflow.trim()));
                    })
                    .collect(Collectors.toList());
        }

        return jsonFiles;
    }

    /**
     * 从JSON文件导入工作流（线程安全）
     */
   /* private void importWorkflowFromJson(String jsonFilePath, String batchNo, String projectId, String sccProductId, String productId) {
        String fileName = new File(jsonFilePath).getName();

        try {
            log.info("[BatchNo:{}] 线程 {} 开始导入工作流：{}", batchNo, Thread.currentThread().getName(), fileName);

            // 1. 调用ImportService的importProcessToTemp方法，将JSON导入到临时表
            importService.importProcessToTemp(jsonFilePath, projectId, productId, sccProductId, batchNo, deleteTaskIds);
            log.info("[BatchNo:{}] JSON文件导入到临时表完成：{}", batchNo, fileName);

            // 2. 调用ImportService的sync方法，将临时表数据同步到正式表
            //importService.sync(batchNo, projectId, productId);
            log.info("[BatchNo:{}] 临时表数据同步到正式表完成：{}", batchNo, fileName);

            log.info("[BatchNo:{}] 线程 {} 工作流导入成功：{}", batchNo, Thread.currentThread().getName(), fileName);

        } catch (Exception e) {
            log.error("[BatchNo:{}] 线程 {} 工作流导入失败：{}，错误：{}",
                    batchNo, Thread.currentThread().getName(), fileName, e.getMessage());
            throw new RuntimeException("工作流导入失败: " + fileName, e);
        }
    }*/


    /**
     * 清理导入文件
     */
    private void cleanupImportFiles(String workDir, String batchNo) {
        try {
            log.info("[BatchNo:{}] 开始清理导入临时文件：{}", batchNo, workDir);

            File dir = new File(workDir);
            if (dir.exists()) {
                deleteDirectory(dir);
                log.info("[BatchNo:{}] 导入临时文件清理完成", batchNo);
            }

        } catch (Exception e) {
            log.warn("[BatchNo:{}] 清理导入临时文件失败：{}", batchNo, e.getMessage());
        }
    }

    /**
     * 递归删除目录
     */
    private void deleteDirectory(File dir) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                if (file.isDirectory()) {
                    deleteDirectory(file);
                } else {
                    file.delete();
                }
            }
        }
        dir.delete();
    }

    /**
     * 预处理阶段结果数据结构
     */
    private static class ImportPreprocessResult {
        private final String tempWorkDir;
        private final List<String> jsonFiles;
        private final Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds;
        private final Tuple3<Integer, Integer, List<String>> taskCount;
        private ConcurrentHashMap<String, String> changeTableIds;

        public ImportPreprocessResult(String tempWorkDir, List<String> jsonFiles,
                                      Tuple2<Map<String, String>, Map<String, String>> deleteTaskIds,
                                      Tuple3<Integer, Integer, List<String>> taskCount, ConcurrentHashMap<String, String> changeTableIds) {
            this.tempWorkDir = tempWorkDir;
            this.jsonFiles = jsonFiles;
            this.deleteTaskIds = deleteTaskIds;
            this.taskCount = taskCount;
            this.changeTableIds = changeTableIds;
        }

        public String getTempWorkDir() {
            return tempWorkDir;
        }

        public List<String> getJsonFiles() {
            return jsonFiles;
        }

        public Tuple2<Map<String, String>, Map<String, String>> getDeleteTaskIds() {
            return deleteTaskIds;
        }

        public Tuple3<Integer, Integer, List<String>> getTaskCount() {
            return taskCount;
        }

        public ConcurrentHashMap<String, String> getChangeTableIds() {
            return changeTableIds;
        }
    }

    /**
     * 并发处理阶段结果数据结构
     */
    private static class ImportConcurrentResult {
        private final int successCount;
        private final int failedCount;
        private final int totalCount;

        public ImportConcurrentResult(int successCount, int failedCount, int totalCount) {
            this.successCount = successCount;
            this.failedCount = failedCount;
            this.totalCount = totalCount;
        }

        public int getSuccessCount() {
            return successCount;
        }

        public int getFailedCount() {
            return failedCount;
        }

        public int getTotalCount() {
            return totalCount;
        }
    }


    @Auth
    @GetMapping("/importExport/logs/{batchNo}")
    public Response<?> getLogs(@PathVariable String batchNo, @RequestParam(defaultValue = "1") int startLine,
                               @RequestParam(defaultValue = "1000") int endLine) {
        User user = ThreadLocalUserUtil.getUser(User.class);
        String filePath = LogReaderUtil.getBatchLogPath(user.getTenantCode(), batchNo);
        List<String> logs = LogReaderUtil.readLogByLineRange(filePath, startLine, endLine);
        return ResponseFactory.makeSuccess(logs);
    }

    /**
     * 下载生成的tar包
     *
     * @param batchNo  批次号
     * @param response HTTP响应对象
     * @return 文件下载
     */
    @Auth
    @GetMapping("/importExport/download/{batchNo}")
    public void downloadTarFile(@PathVariable String batchNo, HttpServletResponse response) {
        try {
            User user = ThreadLocalUserUtil.getUser(User.class);
            String tenantCode = user.getTenantCode();

            log.info("开始下载tar包 - 批次号: {}, 租户: {}", batchNo, tenantCode);

            // 构建tar包文件路径
            String tarFilePath = Paths.get(getExportBasePath(), tenantCode, "download", batchNo + "_export.tar").toString();
            log.info("tar包文件路径: {}", tarFilePath);
            File tarFile = new File(tarFilePath);

            if (!tarFile.exists()) {
                log.error("tar包文件不存在: {}", tarFilePath);
                response.setStatus(HttpServletResponse.SC_NOT_FOUND);
                response.setContentType("application/json;charset=UTF-8");
                response.getWriter().write("{\"success\":false,\"message\":\"文件不存在\"}");
                return;
            }

            log.info("找到tar包文件: {}, 大小: {} bytes", tarFilePath, tarFile.length());

            // 设置响应头
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + batchNo + ".tar\"");
            response.setHeader("Content-Length", String.valueOf(tarFile.length()));
            response.setHeader("Cache-Control", "no-cache");

            // 使用缓冲流传输文件
            try (FileInputStream fis = new FileInputStream(tarFile);
                 BufferedInputStream bis = new BufferedInputStream(fis);
                 OutputStream os = response.getOutputStream();
                 BufferedOutputStream bos = new BufferedOutputStream(os)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = bis.read(buffer)) != -1) {
                    bos.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                bos.flush();
                log.info("tar包下载完成 - 批次号: {}, 传输字节数: {}", batchNo, totalBytes);

            } catch (IOException e) {
                log.error("文件传输过程中发生错误 - 批次号: {}", batchNo, e);
                if (!response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                }
            }

        } catch (Exception e) {
            log.error("下载tar包失败 - 批次号: {}", batchNo, e);
            try {
                if (!response.isCommitted()) {
                    response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
                    response.setContentType("application/json;charset=UTF-8");
                    response.getWriter().write("{\"success\":false,\"message\":\"下载失败: " + e.getMessage() + "\"}");
                }
            } catch (IOException ioException) {
                log.error("写入错误响应失败", ioException);
            }
        }
    }


}
